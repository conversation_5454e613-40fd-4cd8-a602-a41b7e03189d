version: '2'
services:
  app:
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
    links:
      - db
    networks:
      - vistream_portal
    volumes:
      - ./:/var/www
    command: "composer install && php artisan serve"
  db:
    hostname: vistream-portal-db-server
    build:
      context: ./
      dockerfile: ./docker/db/Dockerfile
    networks:
      - vistream_portal
    restart: "no"
    ports:
      - 33061:3306
    volumes:
      - ./docker/db/mysql-data:/var/lib/mysql
      - ./docker/db/mysql-conf:/etc/mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: docker
      MYSQL_PASSWORD: docker
      MYSQL_DATABASE: viportal
networks:
  vistream_portal:
    driver: bridge
