FROM public.ecr.aws/docker/library/debian:bullseye-slim AS builder
#RUN apt-get update && apt-get install -y build-essential wget && \
#    wget https://ftp.gnu.org/gnu/tar/tar-1.35.tar.gz && \
#    tar -xf tar-1.35.tar.gz && \
#    cd tar-1.35 && \
#    env FORCE_UNSAFE_CONFIGURE=1 ./configure && make && make install
RUN apt-get update && apt-get install -y curl

FROM 799121415609.dkr.ecr.eu-central-1.amazonaws.com/fly-laravel:7.4 as base
#fideloper/fly-laravel:8.3 from dockerhub (debian)
#RUN rm -rf /bin/tar
#COPY --from=builder /usr/local/bin/tar /bin/tar

COPY . /var/www/html/
COPY nginx/default /etc/nginx/sites-enabled/default
WORKDIR /var/www/html/

# Environment variable to set non-interactive mode
ENV DEBIAN_FRONTEND=noninteractive

# Update the package list and install necessary packages
RUN apt-get update && apt-get install -y \
    software-properties-common \
    wget \
    gnupg2 \
    lsb-release \
    ca-certificates

# Add the Ondrej PHP repository
RUN add-apt-repository ppa:ondrej/php

# Update the package list again
RUN apt-get update

# Install PHP 7.4 and necessary extensions
RUN apt-get install -y \
    php7.4-mysql \
    php7.4-soap \
    php7.4-xml \
    php7.4-mbstring \
    php7.4-gd \
    php7.4-zip \
    php7.4-dev \
    php-pear \
    git \
    && apt-get clean

# Set the permissions for the storage
RUN chown -R www-data:www-data /var/www/html && \
    chmod -R 755 /var/www/html/storage

# Cleanup
RUN rm -rf /var/lib/apt/lists/*

## php.ini configuration
#php openbasedir settings
#RUN sed -i "s'/var/www/html:/dev/stdout:/tmp'/var/www/html:/dev/stdout:/tmp:/static'g" /etc/php/8.3/fpm/pool.d/www.conf
# Modify PHP settings
# Increase PHP limits
RUN sed -i \
    -e "s/;*upload_max_filesize.*/upload_max_filesize = 100M/" \
    -e "s/;*post_max_size.*/post_max_size = 100M/" \
    -e "s/;*max_execution_time.*/max_execution_time = 900/" \
    -e "s/;*max_input_time.*/max_input_time = 900/" \
    -e "s/;*memory_limit.*/memory_limit = 1024M/" \
    /etc/php/7.4/fpm/php.ini

COPY --from=public.ecr.aws/docker/library/composer:2 /usr/bin/composer /usr/local/bin/composer
RUN composer install
EXPOSE 8080
