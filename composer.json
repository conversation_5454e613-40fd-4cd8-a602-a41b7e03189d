{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": ">=7.4", "calendar/icsfile": "^3.2", "guzzlehttp/guzzle": "^6.3", "laravel/framework": "5.5.*", "laravel/tinker": "~1.0", "laravelcollective/html": "~5.0", "maatwebsite/excel": "~2.1.0", "spatie/laravel-permission": "^2.5", "yajra/laravel-datatables-oracle": "7.5"}, "require-dev": {"fzaninotto/faker": "~1.4", "mockery/mockery": "0.9.*", "phpunit/phpunit": "~5.7"}, "autoload": {"classmap": ["database"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall", "php artisan optimize"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "php artisan optimize"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": true}}}