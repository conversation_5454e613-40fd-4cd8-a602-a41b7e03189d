FROM php:7.4-fpm

RUN apt update 
RUN apt install -y libmcrypt-dev
RUN apt install -y libxml2-dev 
RUN apt install -y libpng-dev 
RUN apt install -y zlib1g-dev 
RUN apt install -y libonig-dev
RUN apt install -y libzip-dev
RUN apt install -y wget 
RUN apt install -y git 
RUN docker-php-ext-install pdo_mysql soap xml mbstring gd zip intl
RUN wget https://getcomposer.org/download/latest-stable/composer.phar
RUN mv composer.phar /usr/local/bin/composer
RUN chmod a+x /usr/local/bin/composer
WORKDIR /var/www
EXPOSE 8000