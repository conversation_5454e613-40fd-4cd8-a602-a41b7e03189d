version: '3'
services:
  app:
    build:
      context: ./
      dockerfile: ./docker/app/Dockerfile
      network: host
    links:
      - db
    volumes:
      - ./:/var/www
    #command: "composer install && php artisan serve"
  db:
    hostname: vistream-portal-db-server
    build:
      context: ./
      dockerfile: ./docker/db/Dockerfile
      network: host
    restart: "no"
    ports:
      - 33061:3306
    volumes:
      - ./docker/db/mysql-data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: docker
      MYSQL_PASSWORD: docker
      MYSQL_DATABASE: viportal
